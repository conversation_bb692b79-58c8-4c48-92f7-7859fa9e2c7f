import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/gift_type.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/features/account/domain/models/user_banks/user_bank_model.dart';
import 'package:gp_stock_app/features/account/logic/add_bank/add_bank_cubit.dart';
import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/deposit/deposit_cubit.dart';
import 'package:gp_stock_app/features/account/logic/deposit/deposit_state.dart';
import 'package:gp_stock_app/features/account/logic/deposit_channel/deposit_channel_cubit.dart';
import 'package:gp_stock_app/features/account/logic/deposit_channel/deposit_channel_state.dart';
import 'package:gp_stock_app/features/account/logic/otp/otp_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_state.dart';
import 'package:gp_stock_app/features/account/screens/add_bank_screen.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';

class DepositScreen extends StatefulWidget {
  const DepositScreen({super.key});

  @override
  State<DepositScreen> createState() => _DepositScreenState();
}

class _DepositScreenState extends State<DepositScreen> {
  final amountController = TextEditingController();
  final orderNumberController = TextEditingController();
  bool _isAmountValid = true;
  bool _validateAmount(String value) {
    final selectedChannel = context.read<DepositChannelCubit>().state.selectedChannel;
    if (value.isEmpty) return true;

    try {
      final amount = double.parse(value);
      final min = selectedChannel?.minAmount ?? 0;
      final max = selectedChannel?.maxAmount ?? double.infinity;
      return amount >= min && amount <= max;
    } catch (_) {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<DepositCubit, DepositState>(
      listenWhen: (previous, current) => previous.depositStatus != current.depositStatus,
      listener: (context, state) {
        if (state.depositStatus.isSuccess) {
          Helper.showFlutterToast('depositedSuccessfully'.tr(), gravity: ToastGravity.CENTER);
          context.read<DepositCubit>().clearForm();
          amountController.clear();
          orderNumberController.clear();
          context.read<UserBankListCubit>().clearForm();
        }
        if (state.depositStatus.isFailed) {
          Helper.showFlutterToast(state.error ?? 'somethingWentWrong'.tr(), gravity: ToastGravity.CENTER);
        }
      },
      child: Scaffold(
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                16.verticalSpace,
                _buildBankSelector(),
                10.verticalSpace,
                _buildAddBankButton(),
                10.verticalSpace,
                _buildRecipientInfo(),
                16.verticalSpace,
                _buildOrderNumberInput(),
                16.verticalSpace,
                _buildSubmitButton(),
                16.verticalSpace,
                SupportWidget(),
                30.verticalSpace,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChannelSelector() {
    return BlocBuilder<DepositChannelCubit, DepositChannelState>(
      builder: (context, state) {
        return CommonDropdown(
          hintText: 'selectChannel'.tr(),
          onChanged: (value) {
            final selectedChannel = state.channelsList?.firstWhere((e) => e.id == int.parse(value.id!));
            context.read<DepositChannelCubit>().updateSelectedChannel(selectedChannel);
          },
          dropDownValue: (state.channelsList ?? [])
              .map((e) => DropDownValue(
                    id: e.id.toString(),
                    value: e.channelName ?? '',
                  ))
              .toList(),
          selectedItem: state.selectedChannel == null
              ? null
              : DropDownValue(
                  id: state.selectedChannel?.id.toString(),
                  value: state.selectedChannel?.channelName ?? '',
                ),
          showSearchBox: false,
          itemBuilder: (context, item, isDisabled, isSelected) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 10.gw),
              padding: EdgeInsets.symmetric(vertical: 8.gh),
              child: Row(
                children: [
                  if (item.icon != null && item.icon!.isNotEmpty)
                    Image.network(
                      item.icon!,
                      width: 40.gw,
                      height: 40.gw,
                      fit: BoxFit.cover,
                    )
                  else
                    Container(
                      width: 40.gw,
                      height: 40.gw,
                      color: Colors.grey[200],
                      child: Icon(Icons.account_balance, color: Colors.grey[500]),
                    ),
                  10.horizontalSpace,
                  Expanded(
                    child: Text(
                      item.value ?? '',
                      style: context.textTheme.regular,
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildBankSelector() {
    return BlocBuilder<UserBankListCubit, UserBankListState>(
      builder: (context, state) {
        final bankItems = state.bankList?.isEmpty ?? true
            ? [
                DropDownValue(
                  id: null,
                  value: 'addBankCard'.tr(),
                )
              ]
            : (state.bankList ?? [])
                .map((e) => DropDownValue(
                      id: e.id.toString(),
                      value: '${e.bankFullName}(${e.bankAccount})',
                    ))
                .toList();
        return ShadowBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'rechargeAmount'.tr(),
                style: context.textTheme.regular.fs16.copyWith(
                  color: context.colorTheme.textPrimary,
                ),
              ),
              16.verticalSpace,
              if (state.bankListFetchStatus == DataStatus.loading)
                ShimmerWidget(
                  height: 40.gh,
                  width: double.infinity,
                  radius: 8.gr,
                  color: context.theme.scaffoldBackgroundColor,
                )
              else
                CommonDropdown<UserBankModel?>(
                  dropDownValue: bankItems,
                  selectedItem: state.selectedBank == null
                      ? null
                      : DropDownValue(
                          id: state.selectedBank?.id.toString(),
                          value: state.selectedBank?.bankFullName ?? '',
                        ),
                  hintText: 'pls_select_the_bank'.tr(),
                  showSearchBox: false,
                  itemBuilder: (context, item, isDisabled, isSelected) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 16.0),
                      child: Text(item.value ?? ''),
                    );
                  },
                  onChanged: (value) {
                    final selectedBank = state.bankList?.firstWhereOrNull((e) => e.id.toString() == value?.id);
                    context.read<UserBankListCubit>().updateSelectedBank(selectedBank);
                    context.read<DepositCubit>().updateSelectedBank(selectedBank);
                  },
                ),
              16.verticalSpace,
              SizedBox(
                child: TextFieldWidget(
                  controller: amountController,
                  onChanged: (value) {
                    setState(() => _isAmountValid = _validateAmount(value ?? ''));
                    context.read<DepositCubit>().updateDepositAmount(value);
                  },
                  hintText: 'enterRechargeAmount'.tr(),
                  errorText: !_isAmountValid ? 'enterRechargeAmount'.tr() : null,
                  textInputType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^[0-9.]*')),
                  ],
                ),
              ),
              16.verticalSpace,

              BlocSelector<AccountInfoCubit, AccountInfoState, ({AccountInfoData? accountInfoData})>(
                selector: (state) => (accountInfoData: state.accountInfo),
                builder: (context, accountState) {
                  return Column(
                    children: [
                      Row(
                        spacing: 8,
                        children: [
                          Text(
                            'balance'.tr(),
                            style: context.textTheme.regular.fs13,
                          ),
                          FlipText(
                            accountState.accountInfoData?.usableCash ?? 0,
                          )
                        ],
                      ),
                    ],
                  );
                },
              ),
              4.verticalSpace,
              // sup *

              BlocBuilder<DepositChannelCubit, DepositChannelState>(
                builder: (context, state) {
                  final selectedChannel = state.selectedChannel;
                  final rangeText =
                      '* ${'minimumAmount'.tr()}: ${selectedChannel?.minAmount?.toStringAsFixed(0) ?? '0'} , ${'maximumAmount'.tr()}: ${selectedChannel?.maxAmount?.toStringAsFixed(0) ?? "0"}';

                  return Text(
                    rangeText,
                    style: context.textTheme.regular.fs12.copyWith(
                      color: Colors.orange,
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAddBankButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        CommonButton(
          title: 'addBank'.tr(),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => MultiBlocProvider(
                  providers: [
                    BlocProvider.value(value: context.read<BankListCubit>()),
                    BlocProvider.value(value: context.read<UserBankListCubit>()),
                    BlocProvider(create: (context) => AddBankCubit()),
                    BlocProvider(create: (context) => OtpCubit()),
                  ],
                  child: AddBankScreen(),
                ),
              ),
            );
          },
          prefix: Icon(Icons.add, size: 18, color: Colors.white),
          prefixLeftPadding: 8.gw,
          height: 36.gh,
          width: 110.gw,
          fontSize: 12.gsp,
          textColor: Colors.white,
          radius: 8.gr,
          color: context.theme.primaryColor,
        ),
      ],
    );
  }

  Widget _buildRecipientInfo() {
    return ShadowBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'receiver'.tr(),
            style: context.textTheme.regular.fs16.copyWith(
              color: context.colorTheme.textPrimary,
            ),
          ),
          10.verticalSpace,
          _buildChannelSelector(),
          10.verticalSpace,
          BlocBuilder<DepositChannelCubit, DepositChannelState>(
            builder: (context, state) {
              final selectedChannel = state.selectedChannel;
              final giftType = selectedChannel?.giveGiftType ?? 1;
              final cashbackAmount =
                  ((selectedChannel?.constantGiveRate ?? 0) / 100) * (double.tryParse(amountController.text) ?? 0);
              return Wrap(
                children: [
                  Text.rich(
                    TextSpan(
                      text: GiveGiftType.fromType(giftType).label.tr(),
                      style: context.textTheme.regular.fs12.copyWith(
                        color: context.colorTheme.textRegular,
                      ),
                      children: [
                        TextSpan(
                          text: ' ${cashbackAmount.toStringAsFixed(2)} 元',
                          style: context.textTheme.primary.fs12.w600.copyWith(
                            color: Colors.orange,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
          10.verticalSpace,
          _infoRow('receiver'.tr(), 'pleaseContactCustomerService'.tr()),
          Divider(color: Colors.grey[200], thickness: 0.5),
          _infoRow('cardNumber'.tr(), 'pleaseContactCustomerService'.tr()),
          Divider(color: Colors.grey[200], thickness: 0.5),
          _infoRow('bank'.tr(), 'pleaseContactCustomerService'.tr()),
          Divider(color: Colors.grey[200], thickness: 0.5),
          _infoRow('openingBank'.tr(), 'pleaseContactCustomerService'.tr()),
        ],
      ),
    );
  }

  Widget _buildOrderNumberInput() {
    return ShadowBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'transferor'.tr(),
            style: context.textTheme.regular.fs16.copyWith(
              color: context.colorTheme.textPrimary,
            ),
          ),
          SizedBox(height: 8),
          TextFieldWidget(
            controller: orderNumberController,
            onChanged: (value) => context.read<DepositCubit>().updateOrderNumber(value),
            hintText: 'pleaseEnterYourNameForTransfer'.tr(),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return BlocSelector<DepositCubit, DepositState, ({bool isFormValid, bool isLoading})>(
      selector: (state) =>
          (isFormValid: (state.isFormValid ?? false) && _isAmountValid, isLoading: state.depositStatus.isLoading),
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: CommonButton(
            onPressed: () {
              context.read<DepositCubit>().pay(
                    payAmount: amountController.text,
                    userBankId: context.read<UserBankListCubit>().state.selectedBank!.id,
                    orderNumber: orderNumberController.text.isEmpty ? null : orderNumberController.text,
                    sysBankCardId: context.read<DepositChannelCubit>().state.selectedChannel?.id ?? 0,
                  );
            },
            title: 'recharge'.tr(),
            showLoading: state.isLoading,
            enable: state.isFormValid,
          ),
        );
      },
    );
  }

  Widget _infoRow(String title, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 80.gw,
            child: Text(
              title,
              style: context.textTheme.regular.copyWith(
                color: context.colorTheme.textPrimary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: context.textTheme.regular.copyWith(
                color: context.colorTheme.textRegular,
              ),
              softWrap: true,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}
