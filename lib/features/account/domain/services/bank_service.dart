import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/features/account/domain/models/bank/bank_model.dart';
import 'package:gp_stock_app/features/account/domain/models/deposit_channel/deposit_channel_model.dart';
import 'package:gp_stock_app/features/account/domain/models/proxy_pay_channel/proxy_pay_channel_model.dart';
import 'package:gp_stock_app/features/account/domain/models/user_banks/user_bank_model.dart';
import 'package:gp_stock_app/features/account/domain/models/user_wallet/user_wallet_model.dart';
import 'package:gp_stock_app/features/account/domain/models/withdrawal_config/withdrawal_config.dart';
import 'package:gp_stock_app/features/account/domain/repository/bank_repository.dart';
import 'package:injectable/injectable.dart';

import '../models/deposit_records/deposit_record.dart';
import '../models/pay_order/pay_order_response.dart';
import '../models/withdrawal_records/withdrawal_record.dart';

@Singleton(as: BankRepository)
class BankService implements BankRepository {
  @override
  Future<ResponseResult<List<BankModel>>> getBankList() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getBankList,
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: List<BankModel>.from(response.data['data'].map((e) => BankModel.fromJson(e))),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<List<UserBankModel>>> getUserBankList() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getUserBankList,
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: List<UserBankModel>.from(response.data['data'].map((e) => UserBankModel.fromJson(e))),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<List<UserWalletModel>>> getUserWalletList({String? bankCode}) async {
    try {
      // Build query parameters if bankCode is provided
      String endpoint = ApiEndpoints.getUserWalletList;
      if (bankCode != null && bankCode.isNotEmpty) {
        endpoint += "?bankCode=$bankCode";
      }

      final Response response = await NetworkProvider().get(
        endpoint,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final List<dynamic> walletData = response.data['data'] ?? [];
          return ResponseResult(
            data: walletData.map((e) => UserWalletModel.fromJson(e)).toList(),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<bool>> addUserBank(
      {required String bankCardNo, required int systemBankId, required String mobile, required String smsCode}) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.addUserBank,
        data: {
          'bankCardNo': bankCardNo,
          'systemBankId': systemBankId,
          'mobile': mobile,
          'smsCode': smsCode,
        },
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<bool>> pay({
    required int payAmount,
    required int userBankId,
    String? orderNumber,
    required int sysBankCardId,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'payAmount': payAmount,
        'userBankId': userBankId,
        'sysBankCardId': sysBankCardId,
      };

      if (orderNumber != null && orderNumber.isNotEmpty) {
        data['chargeNo'] = orderNumber;
      }

      final Response response = await NetworkProvider().post(
        ApiEndpoints.getUserBankInfo,
        data: data,
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<bool>> withdraw({
    required int withdrawAmount,
    required int userBankId,
    required String password,
    int? type,
    int? channelId,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.withdraw,
        data: {
          'amount': withdrawAmount,
          'userBankCardId': userBankId,
          'password': password,
          if (type != null) 'type': type,
          if (channelId != null) 'channelId': channelId,
        },
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<WithdrawalConfig>> getWithdrawConfig() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.withdrawalConfig,
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: WithdrawalConfig.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<WithdrawalRecordResponse>> getWithdrawalRecords(int pageNumber, int pageSize) async {
    try {
      final Response response = await NetworkProvider().get(
        "${ApiEndpoints.withdrawalRecords}?pageNumber=$pageNumber&pageSize=$pageSize",
        isAuthRequired: true,
      );

      if (response.statusCode == 200 && response.data['code'] == 0) {
        return ResponseResult(
          data: WithdrawalRecordResponse.fromJson(response.data['data']),
        );
      } else {
        return ResponseResult(
          error: response.data['msg'] ?? 'error'.tr(),
        );
      }
    } on DioException catch (e) {
      return ResponseResult(
        error: e.message ?? 'error'.tr(),
      );
    } catch (e) {
      return ResponseResult(
        error: e.toString(),
      );
    }
  }

  // getDepositRecords
  @override
  Future<ResponseResult<DepositRecordResponse>> getDepositRecords(int pageNumber, int pageSize) async {
    try {
      final Response response = await NetworkProvider().get(
        "${ApiEndpoints.depositRecords}?pageNumber=$pageNumber&pageSize=$pageSize",
        isAuthRequired: true,
      );
      if (response.statusCode == 200 && response.data['code'] == 0) {
        return ResponseResult(
          data: DepositRecordResponse.fromJson(response.data['data']),
        );
      } else {
        return ResponseResult(
          error: response.data['msg'] ?? 'error'.tr(),
        );
      }
    } on DioException catch (e) {
      return ResponseResult(
        error: e.message ?? 'error'.tr(),
      );
    } catch (e) {
      return ResponseResult(
        error: e.toString(),
      );
    }
  }

  @override
  Future<ResponseResult<List<DepositChannelModel>>> getChannelList() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getDepositChannelsList,
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final List<dynamic> records = response.data['data'] ?? [];
          return ResponseResult(
            data: records.map((e) => DepositChannelModel.fromJson(e)).toList(),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<List<ProxyPayChannelModel>>> getProxyPayChannelList() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.listProxyPayChannel,
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: List<ProxyPayChannelModel>.from(response.data['data'].map((e) => ProxyPayChannelModel.fromJson(e))),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<PayOrderResponse>> getPayOrders({
    required int pageNumber,
    required int pageSize,
    String? createDateStart,
    String? createDateEnd,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParams = {
        'pageNumber': pageNumber.toString(),
        'pageSize': pageSize.toString(),
      };

      // Add optional parameters if provided
      if (createDateStart != null) {
        queryParams['createDateStart'] = createDateStart;
      }
      if (createDateEnd != null) {
        queryParams['createDateEnd'] = createDateEnd;
      }

      // Convert query parameters to URL query string
      final queryString = queryParams.entries.map((e) => '${e.key}=${e.value}').join('&');

      final Response response = await NetworkProvider().get(
        "${ApiEndpoints.payOrder}?$queryString",
        isAuthRequired: true,
      );

      if (response.statusCode == 200 && response.data['code'] == 0) {
        return ResponseResult(
          data: PayOrderResponse.fromJson(response.data['data']),
        );
      } else {
        return ResponseResult(
          error: response.data['msg'] ?? 'error'.tr(),
        );
      }
    } on DioException catch (e) {
      return ResponseResult(
        error: e.message ?? 'error'.tr(),
      );
    } catch (e) {
      return ResponseResult(
        error: e.toString(),
      );
    }
  }
}
