import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/features/account/domain/models/bank/bank_model.dart';
import 'package:gp_stock_app/features/account/domain/models/deposit_channel/deposit_channel_model.dart';
import 'package:gp_stock_app/features/account/domain/models/proxy_pay_channel/proxy_pay_channel_model.dart';
import 'package:gp_stock_app/features/account/domain/models/user_banks/user_bank_model.dart';
import 'package:gp_stock_app/features/account/domain/models/user_wallet/user_wallet_model.dart';
import 'package:gp_stock_app/features/account/domain/models/withdrawal_config/withdrawal_config.dart';

import '../models/deposit_records/deposit_record.dart';
import '../models/pay_order/pay_order_response.dart';
import '../models/withdrawal_records/withdrawal_record.dart';

abstract class BankRepository {
  Future<ResponseResult<List<BankModel>>> getBankList();
  Future<ResponseResult<List<UserBankModel>>> getUserBankList();
  Future<ResponseResult<List<UserWalletModel>>> getUserWalletList({String? bankCode});
  Future<ResponseResult<bool>> addUserBank(
      {required String bankCardNo, required int systemBankId, required String mobile, required String smsCode});
  Future<ResponseResult<bool>> pay({
    required int payAmount,
    required int userBankId,
    String? orderNumber,
    required int sysBankCardId,
  });
  Future<ResponseResult<bool>> withdraw({
    required int withdrawAmount,
    required int userBankId,
    required String password,
    int? type,
    int? channelId,
  });
  Future<ResponseResult<WithdrawalConfig>> getWithdrawConfig();
  Future<ResponseResult<WithdrawalRecordResponse>> getWithdrawalRecords(int pageNumber, int pageSize);
  Future<ResponseResult<DepositRecordResponse>> getDepositRecords(int pageNumber, int pageSize);
  Future<ResponseResult<PayOrderResponse>> getPayOrders({
    required int pageNumber,
    required int pageSize,
    String? createDateStart,
    String? createDateEnd,
  });
  Future<ResponseResult<List<DepositChannelModel>>> getChannelList();
  Future<ResponseResult<List<ProxyPayChannelModel>>> getProxyPayChannelList();
}
