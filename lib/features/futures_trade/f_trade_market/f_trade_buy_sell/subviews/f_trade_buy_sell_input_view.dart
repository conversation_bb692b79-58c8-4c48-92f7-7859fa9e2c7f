import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_direction_button.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/logic/f_trade_buy_sell_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/f_trade_buy_sell_Text_field.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/subviews.dart';

import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FTradeBuySellInputView extends StatefulWidget {
  final double? accountUsableCash;
  final double latestPrice;
  final FTradeConfigModel? fTradeConfigModel;
  final UserInputController userInputState;
  final void Function(UserInputController oldFTradeUserInputController) onUserInputControllerChanged;
  final TextEditingController priceEditingController;
  final TextEditingController numberEditingController;
  final FtradeLongShortActionsController buyActionsController;
  final FtradeLongShortActionsController sellActionsController;

  const FTradeBuySellInputView({
    super.key,
    this.accountUsableCash,
    required this.latestPrice,
    required this.fTradeConfigModel,
    required this.userInputState,
    required this.onUserInputControllerChanged,
    required this.priceEditingController,
    required this.numberEditingController,
    required this.buyActionsController,
    required this.sellActionsController,
  });

  @override
  State<FTradeBuySellInputView> createState() => _FTradeBuySellInputViewState();
}

class _FTradeBuySellInputViewState extends State<FTradeBuySellInputView> {
  /// 交易价格
  late FocusNode priceFocusNode;

  /// 交易X手
  late FocusNode numberFocusNode;

  /// 选中的账户类型 (null = 现货交易, 其他值 = 合约ID)
  String? selectedAccountId;

  @override
  void initState() {
    super.initState();
    priceFocusNode = FocusNode();
    numberFocusNode = FocusNode();

    // Initialize with spot trading (null) as default, similar to H5
    selectedAccountId = null;

    priceFocusNode.addListener(_onPriceForceChange);
    numberFocusNode.addListener(_onNumberForceChange);
  }

  @override
  void dispose() {
    priceFocusNode.dispose();
    numberFocusNode.dispose();
    super.dispose();
  }

  void _handlePriceTextFieldTap() {
    widget.priceEditingController.selection = TextSelection(
      baseOffset: 0,
      extentOffset: widget.priceEditingController.text.length,
    );
  }

  void _onPriceForceChange() {
    if (priceFocusNode.hasFocus) {
      widget.onUserInputControllerChanged(widget.userInputState.copyWith(
        inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
        orderFraction: null,
        priceHasFocus: true,
      ));
      return;
    }

    double currentValue = double.tryParse(widget.priceEditingController.text) ?? 0.0;
    if (currentValue <= 0) {
      currentValue = 0;
    } else if (currentValue > ********) {
      currentValue = ********;
    }
    widget.onUserInputControllerChanged(widget.userInputState.copyWith(
      inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
      orderFraction: null,
      price: currentValue,
      priceHasFocus: false,
    ));
  }

  void _handleNumberTextFieldTap() {
    widget.numberEditingController.selection = TextSelection(
      baseOffset: 0,
      extentOffset: widget.numberEditingController.text.length,
    );
  }

  void _onNumberForceChange() {
    if (numberFocusNode.hasFocus) {
      widget.onUserInputControllerChanged(widget.userInputState.copyWith(
        inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
        orderFraction: null,
        numberHasFocus: true,
      ));
      return;
    }
    final currentValue = double.tryParse(widget.numberEditingController.text) ?? 0.0;
    widget.onUserInputControllerChanged(widget.userInputState.copyWith(
      inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
      orderFraction: null,
      number: currentValue,
      numberHasFocus: false,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: Column(
          children: AnimationConfiguration.toStaggeredList(
        duration: const Duration(milliseconds: 400),
        childAnimationBuilder: (widget) => SlideAnimation(
          horizontalOffset: 50.0,
          child: FadeInAnimation(
            child: widget,
          ),
        ),
        children: [
          _buildFrom(context),
          FtradeLongShortActionsView(
            longActionsController: widget.buyActionsController,
            shortActionsController: widget.sellActionsController,
          )
        ],
      )),
    );
  }

  Widget _buildFrom(BuildContext context) {
    var userInputState = widget.userInputState;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      spacing: 8,
      children: [
        _buildTradeDirectionSection(
          context: context,
          selectedTradeDirection: userInputState.tradeDirection,
          onTradeDirectionChanged: (newTradeDirection) {
            final temp = userInputState.copyWith(tradeDirection: newTradeDirection);
            widget.onUserInputControllerChanged(temp);
          },
        ),
        Row(
          spacing: 8,
          children: [
            Expanded(
              child: _buildAccountSelectionDropdown(context),
            ),
            Expanded(
              child: userInputState.priceType == PriceType.market
                  ? Container(
                      height: 35.gh,
                      padding: EdgeInsets.symmetric(horizontal: 12.gw),
                      decoration: BoxDecoration(
                        color: context.theme.inputDecorationTheme.fillColor,
                        borderRadius: BorderRadius.circular(5.gr),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'realTimePrice'.tr(),
                            style: context.textTheme.primary,
                          ),
                        ],
                      ),
                    )
                  : FTradeBuySellTextField(
                      onIncrementPressed: () {
                        double currentValue = userInputState.price + 1;
                        if (currentValue > ********) {
                          currentValue = ********;
                        }
                        widget.onUserInputControllerChanged(userInputState.copyWith(
                          price: currentValue,
                          inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                        ));
                      },
                      onDecrementPressed: () {
                        double currentValue = userInputState.price - 1;
                        if (currentValue <= 0) {
                          currentValue = 0;
                        }
                        widget.onUserInputControllerChanged(userInputState.copyWith(
                          price: currentValue,
                          inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                        ));
                      },
                      controller: widget.priceEditingController,
                      focusNode: priceFocusNode,
                      onTap: _handlePriceTextFieldTap,
                    ),
            ),
          ],
        ),
        Row(
          spacing: 8,
          children: [
            Expanded(
              child: CommonDropdown(
                height: 35.gh,
                showSearchBox: false,
                isEnabled: widget.fTradeConfigModel == null ? false : true,
                selectedItem: userInputState.priceType == PriceType.market
                    ? DropDownValue(id: 'marketOrder', value: 'marketOrder'.tr())
                    : DropDownValue(id: 'limitOrder', value: 'limitOrder'.tr()),
                dropDownValue: [
                  DropDownValue(id: 'marketOrder', value: 'marketOrder'.tr()),
                  DropDownValue(id: 'limitOrder', value: 'limitOrder'.tr()),
                ],
                onChanged: (value) {
                  if (value.id == 'marketOrder') {
                    final temp = userInputState.copyWith(
                      priceType: PriceType.market,
                      inputNumberEnum: FTradeBuySellUserInputNumberEnum.init,
                    );
                    widget.onUserInputControllerChanged(temp);
                  }
                  if (value.id == 'limitOrder') {
                    final temp = userInputState.copyWith(
                      priceType: PriceType.limit,
                      inputNumberEnum: FTradeBuySellUserInputNumberEnum.init,
                    );
                    widget.onUserInputControllerChanged(temp);
                  }
                },
                hintText: 'trading_methods'.tr(),
                borderRadius: 5.gr,
                textStyle: context.textTheme.regular.fs13.copyWith(color: context.colorTheme.textPrimary),
              ),
            ),
            if (widget.fTradeConfigModel == null || userInputState.number == -1)
              Expanded(
                child: Container(
                  height: 35.gh,
                  padding: EdgeInsets.symmetric(horizontal: 12.gw),
                  decoration: BoxDecoration(
                    color: context.theme.scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(5.gr),
                  ),
                ),
              ),
            // 网络延迟导致服务器数据不足 会先计算出0 然后计算出正确值 导致闪烁
            if (widget.latestPrice != 0 && widget.fTradeConfigModel != null && userInputState.number != -1)
              Expanded(
                child: FTradeBuySellTextField(
                  onDecrementPressed: userInputState.canNumberDecrement == false
                      ? null
                      : () {
                          double currentValue = userInputState.number - widget.fTradeConfigModel!.minTradeQuantity;
                          widget.onUserInputControllerChanged(userInputState.copyWith(
                            inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                            orderFraction: null,
                            number: currentValue,
                          ));
                        },
                  onIncrementPressed: userInputState.canNumberIncrement == false
                      ? null
                      : () {
                          double currentValue = userInputState.number + widget.fTradeConfigModel!.minTradeQuantity;
                          widget.onUserInputControllerChanged(userInputState.copyWith(
                            inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                            orderFraction: null,
                            number: currentValue,
                          ));
                        },
                  controller: widget.numberEditingController,
                  focusNode: numberFocusNode,
                  onTap: _handleNumberTextFieldTap,
                ),
              ),
          ],
        ),
        _BalanceLabel(balance: widget.accountUsableCash, currency: null),
        _FractionSection(
          selectedFraction: userInputState.orderFraction,
          onFractionSelected: (selected) {
            widget.onUserInputControllerChanged(userInputState.copyWith(
              inputNumberEnum: FTradeBuySellUserInputNumberEnum.selcted,
              orderFraction: selected,
            ));
          },
        ),
      ],
    );
  }

  /// Build account selection dropdown similar to H5 accountOptions
  Widget _buildAccountSelectionDropdown(BuildContext context) {
    // Get account options similar to H5 implementation
    final accountCubit = context.read<AccountCubit>();
    final accountOptions = accountCubit.getContractsByMarketType(
      MainMarketType.cnFutures, // For futures trading
      true, // isIndexTrading - for now set to true to get spot trading only
    );

    // Find selected item
    final selectedItem = accountOptions.firstWhere(
      (option) => option.id == selectedAccountId,
      orElse: () => accountOptions.first, // Default to first option (spot trading)
    );

    return CommonDropdown(
      height: 35.gh,
      showSearchBox: false,
      isEnabled: true,
      selectedItem: selectedItem,
      dropDownValue: accountOptions,
      onChanged: (value) {
        setState(() {
          selectedAccountId = value.id;
        });
        // TODO: Add any calculation logic here when account changes
        // Similar to H5 onSelectAccount function
      },
      hintText: 'selectAccount'.tr(),
      borderRadius: 5.gr,
      textStyle: context.textTheme.primary,
    );
  }

  Widget _buildTradeDirectionSection({
    required BuildContext context,
    required TradeDirection selectedTradeDirection,
    required void Function(TradeDirection oldTradeDirection) onTradeDirectionChanged,
  }) {
    final labels = (buy: 'open'.tr(), sell: 'availableToClose'.tr());
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'tradeDirection'.tr(),
          style: context.textTheme.primary,
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              spacing: 8,
              children: [
                TradeDirectionButton(
                  text: labels.buy,
                  color: context.upColor,
                  onPressed: () => onTradeDirectionChanged(TradeDirection.buy),
                  isSelected: selectedTradeDirection == TradeDirection.buy,
                ),
                TradeDirectionButton(
                  text: labels.sell,
                  color: context.downColor,
                  onPressed: () => onTradeDirectionChanged(TradeDirection.sell),
                  isSelected: selectedTradeDirection == TradeDirection.sell,
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

/*
============================================================================================================================
BalanceLabel
============================================================================================================================
*/

class _BalanceLabel extends StatelessWidget {
  /// 余额
  final double? balance;

  /// 货币单位
  final String? currency;

  const _BalanceLabel({
    required this.balance,
    required this.currency,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'balance'.tr(),
              style: context.textTheme.regular,
            ),
            if (balance == null) ShimmerWidget(height: 18, width: 88),
            if (balance != null)
              AnimatedFlipCounter(
                thousandSeparator: ',',
                fractionDigits: 2,
                suffix: ' ${currency ?? 'CNY'}',
                textStyle: context.textTheme.primary.w700.ffAkz,
                value: balance!,
              )
          ],
        ),
      ],
    );
  }
}

/*
============================================================================================================================
FractionSection
============================================================================================================================
*/

class _FractionSection extends StatelessWidget {
  final OrderFraction? selectedFraction;
  final void Function(OrderFraction?) onFractionSelected;

  const _FractionSection({
    required this.selectedFraction,
    required this.onFractionSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: List.generate(OrderFraction.values.length, (index) {
        final fraction = OrderFraction.values[index];
        final isSelected = selectedFraction == fraction;
        return _FractionButton(
          fraction: fraction,
          isSelected: isSelected,
          onTap: () => onFractionSelected(isSelected ? null : fraction),
        );
      }),
    );
  }
}

class _FractionButton extends StatelessWidget {
  const _FractionButton({
    required this.fraction,
    required this.isSelected,
    required this.onTap,
  });

  final OrderFraction fraction;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 30.gh,
        width: 75.gw,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.gr),
          color: isSelected ? context.theme.primaryColor : context.theme.inputDecorationTheme.fillColor,
        ),
        child: Center(
          child: Text(
            fraction.label,
            style: context.textTheme.regular.copyWith(
              color: isSelected ? context.theme.cardColor : context.colorTheme.textPrimary,
            ),
          ),
        ),
      ),
    );
  }
}
