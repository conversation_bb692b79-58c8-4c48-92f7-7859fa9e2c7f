import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/trade/trade_handling_fee_config_entity.dart';
import 'package:gp_stock_app/core/utils/fee_calculator.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/logic/f_trade_acct_position_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_service.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_state_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/domain/f_trade_buy_sell_service.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'f_trade_buy_sell_state.dart';

/// 持有和更新一个交易所的数据,其他未显示交易所的数据放入缓存
class FTradeBuySellCubit extends Cubit<FTradeBuySellState> with UserInputNumberHelper {
  final FTradeKLineCubit _fTradeKLineCubit;
  late final StreamSubscription _fTradeKlineSubscription;
  final FTradeAcctPositionCubit _fTradeAcctPositionCubit;
  late final StreamSubscription _fTradeAcctPositionSubscription;

  /// 证劵信息
  final FTradeListItemModel itemModel;

  Timer? _pollUsableCashTimer;

  FTradeBuySellCubit(super.initialState, this.itemModel, this._fTradeKLineCubit, this._fTradeAcctPositionCubit) {
    emit(state.copyWith(
      longActionsController: FtradeLongShortActionsController.defaultInit(isBuy: true),
      shortActionsController: FtradeLongShortActionsController.defaultInit(isBuy: false),
    ));
    _fTradeKlineSubscription = _fTradeKLineCubit.stream.listen((fTradeKLineState) {
      String? productCode = fTradeKLineState.fTradeInfoModel?.productCode;
      double? fTradeLatestPrice = fTradeKLineState.fTradeInfoModel?.latestPrice;
      int? fTradeLastTradeTime = fTradeKLineState.fTradeInfoModel?.lastTradeTime;
      emit(state.copyWith(
        productCode: productCode,
        fTradeLatestPrice: fTradeLatestPrice,
        fTradeLastTradeTime: fTradeLastTradeTime,
      ));
      _updateTradeOrderDetail();
    });
    _fTradeAcctPositionSubscription = _fTradeAcctPositionCubit.stream.listen((fTradeAcctPositionState) {
      List<FTradeAcctOrderRecords> records = fTradeAcctPositionState.orderModel?.records ?? [];
      emit(state.copyWith(fTradeAcctPositionRecords: records));
      _updateTradeOrderDetail();
    });
  }

  /*
  ============================================================================================================================
  Polling
  ============================================================================================================================
  */

  @override
  Future<void> close() {
    _pollUsableCashTimer?.cancel();
    _fTradeKlineSubscription.cancel();
    _fTradeAcctPositionSubscription.cancel();
    return super.close();
  }

  void startUsableCashPolling() {
    _pollUsableCashTimer?.cancel();
    _pollUsableCashTimer = Timer.periodic(const Duration(milliseconds: 3500), (_) {
      fetchUsableCash();
    });
  }

  /*
  ============================================================================================================================
  User Actions
  ============================================================================================================================
  */

  void handleUserInputControllerChanged(UserInputController newCtrl) {
    // 切换 开仓[买] / 平仓[卖]
    final oldTradeDirection = state.userInputController.tradeDirection;
    final newTradeDirection = newCtrl.tradeDirection;
    if (newCtrl.tradeDirection == TradeDirection.sell) {
      if (state.fTradeAcctPositionRecords == null) {
        return;
      }
      if (state.fTradeAcctPositionRecords!.isEmpty) {
        GPEasyLoading.showToast('noAvailablePosition'.tr());
        return;
      }
      if (oldTradeDirection != newTradeDirection) {
        // 切换买入卖出 会清空用户选中 恢复初始化
        final empty = UserInputController();
        newCtrl = empty.copyWith(tradeDirection: TradeDirection.sell);
      }
      emit(state.copyWith(userInputController: newCtrl));
      _updateTradeOrderDetail();
      return;
    }
    if (oldTradeDirection != newTradeDirection) {
      // 切换买入卖出 会清空用户选中 恢复初始化
      final empty = UserInputController();
      newCtrl = empty.copyWith(tradeDirection: TradeDirection.buy);
    }
    emit(state.copyWith(userInputController: newCtrl));
    _updateTradeOrderDetail();
  }

  /// 数组只有空和1个元素的情况 所以设置1等于选中了空
  final int _magicIdx = 1;

  void handleLongActionSelectedIdx(int idx) {
    emit(state.copyWith(
      longActionsController: state.longActionsController.copyWith(selectedOptionIdx: idx),
      shortActionsController: state.shortActionsController.copyWith(selectedOptionIdx: _magicIdx),
      userInputController: state.userInputController.copyWith(inputNumberEnum: FTradeBuySellUserInputNumberEnum.init),
    ));
    _updateTradeOrderDetail();
  }

  void handleShortActionSelectedIdx(int idx) {
    emit(state.copyWith(
      longActionsController: state.longActionsController.copyWith(selectedOptionIdx: _magicIdx),
      shortActionsController: state.shortActionsController.copyWith(selectedOptionIdx: idx),
      userInputController: state.userInputController.copyWith(inputNumberEnum: FTradeBuySellUserInputNumberEnum.init),
    ));
    _updateTradeOrderDetail();
  }

  /// Handle leverage multiplier change from dropdown
  void onLeverageMultipleChanged(int newMultiple) {
    // Update the config model with the new selected multiple
    if (state.fTradeConfigModel != null) {
      final updatedConfig = FTradeConfigModel()
        ..id = state.fTradeConfigModel!.id
        ..market = state.fTradeConfigModel!.market
        ..marginRatio = state.fTradeConfigModel!.marginRatio
        ..closeRadio = state.fTradeConfigModel!.closeRadio
        ..currency = state.fTradeConfigModel!.currency
        ..maxEmptyMatch = state.fTradeConfigModel!.maxEmptyMatch
        ..minEmptyMatch = state.fTradeConfigModel!.minEmptyMatch
        ..maxManyMatch = state.fTradeConfigModel!.maxManyMatch
        ..minManyMatch = state.fTradeConfigModel!.minManyMatch
        ..maxIncrease = state.fTradeConfigModel!.maxIncrease
        ..minIncrease = state.fTradeConfigModel!.minIncrease
        ..maxTradeQuantity = state.fTradeConfigModel!.maxTradeQuantity
        ..minTradeQuantity = state.fTradeConfigModel!.minTradeQuantity
        ..multiple = newMultiple // Update the selected multiple
        ..multipleList = state.fTradeConfigModel!.multipleList
        ..rebateStatus = state.fTradeConfigModel!.rebateStatus
        ..reverseTradeStatus = state.fTradeConfigModel!.reverseTradeStatus
        ..sellModel = state.fTradeConfigModel!.sellModel
        ..sellValue = state.fTradeConfigModel!.sellValue
        ..status = state.fTradeConfigModel!.status
        ..symbol = state.fTradeConfigModel!.symbol
        ..tradeModel = state.fTradeConfigModel!.tradeModel
        ..warnRadio = state.fTradeConfigModel!.warnRadio;

      emit(state.copyWith(fTradeConfigModel: updatedConfig));
      _updateTradeOrderDetail(); // Recalculate with new leverage
    }
  }

  /*
  ============================================================================================================================
  Counting logic
  ============================================================================================================================
  */

  void _updateTradeOrderDetail() {
    if (state.userInputController.tradeDirection == TradeDirection.buy) {
      _updateBuyTradeOrderDetail();
    } else if (state.userInputController.tradeDirection == TradeDirection.sell) {
      _updateSellTradeOrderDetail();
    }
  }

  /// 计算更新[卖出]交易订单的详细信息
  void _updateSellTradeOrderDetail() {
    final selectPrice = _countUserInputBaseState('price');
    final costPerUnit = _countUserInputBaseState('costPerUnit');
    FTradeAcctOrderRecords? curPosition = state.selectedHandlePosition();

    double tempNumber = 0;
    OrderFraction? orderFraction;
    switch (state.userInputController.inputNumberEnum) {
      case FTradeBuySellUserInputNumberEnum.init:
        orderFraction = null;
        // 网络延迟导致服务器数据不足 会先计算出0 然后计算出正确值 导致闪烁
        if (state.accountUsableCash != null && // 余额接口正常
            state.fTradeConfigModel != null && // 证券配置正常
            (state.buyCalculateConfigList.isNotEmpty || state.sellCalculateConfigList.isNotEmpty) /** 买卖手续配置正常 */) {
          tempNumber = curPosition?.restNum ?? 0;
        } else {
          tempNumber = state.userInputController.number;
        }
        break;
      case FTradeBuySellUserInputNumberEnum.selcted:
        orderFraction = state.userInputController.orderFraction;

        final OrderFraction selectedOrderFraction;
        if (state.userInputController.orderFraction == null) {
          selectedOrderFraction = OrderFraction.full;
        } else {
          selectedOrderFraction = state.userInputController.orderFraction!;
        }
        tempNumber = (curPosition?.restNum ?? 0) * selectedOrderFraction.fraction;
        break;
      case FTradeBuySellUserInputNumberEnum.input:
        orderFraction = null;
        tempNumber = state.userInputController.number;
        break;
    }
    final (:number, :canDe, :canIn) =
        countUserInputNumber(number: tempNumber, maxNumber: curPosition?.restNum ?? 0, minNumber: 1);
    double longNumber = 0;
    double shortNumber = 0;
    //
    double longFee = 0;
    double shortFee = 0;
    if (state.selectedLongPosition() != null) {
      longNumber = number;
      final double fee = _calculateFee(
        buyOrSell: state.userInputController.tradeDirection == TradeDirection.buy,
        tradeAmount: costPerUnit * longNumber,
        tradeNum: longNumber,
      );
      longFee = fee;
    }
    if (state.selectedShortPosition() != null) {
      shortNumber = number;
      final double fee = _calculateFee(
        buyOrSell: state.userInputController.tradeDirection == TradeDirection.buy,
        tradeAmount: costPerUnit * shortNumber,
        tradeNum: shortNumber,
      );
      shortFee = fee;
    }
    //
    emit(state.copyWith(
      userInputController: state.userInputController.copyWith(
        price: selectPrice,
        number: number,
        canNumberDecrement: canDe,
        canNumberIncrement: canIn,
        orderFraction: orderFraction,
      ),
      longActionsController: state.longActionsController.copyWith(
        confirmTitle: 'sellLong'.tr(),
        confirmEnabled: state.selectedLongPosition() != null,
        isLoadingConfirmBtn: state.fTradeConfigDataStatus == DataStatus.loading,
        needShowOptions: true,
        optionTitles: state.holdLongPosition() == null ? [] : [state.holdLongPosition()!.displayByOneRow()],
        displayRowInfoList: FtradeLongShortActionsController.makeDisplayRowInfoList(
          'sell',
          state.selectedLongPosition()?.restNum ?? 0,
          longFee,
          costPerUnit * longNumber,
          costPerUnit * longNumber + longFee,
          state.selectedLongPosition()?.marginRatio ?? 0.0,
        ),
      ),
      shortActionsController: state.shortActionsController.copyWith(
        confirmTitle: 'sellShort'.tr(),
        confirmEnabled: state.selectedShortPosition() != null,
        isLoadingConfirmBtn: state.fTradeConfigDataStatus == DataStatus.loading,
        needShowOptions: true,
        optionTitles: state.holdShortPosition() == null ? [] : [state.holdShortPosition()!.displayByOneRow()],
        displayRowInfoList: FtradeLongShortActionsController.makeDisplayRowInfoList(
          'sell',
          state.selectedShortPosition()?.restNum ?? 0,
          shortFee,
          costPerUnit * shortNumber,
          costPerUnit * shortNumber + shortFee,
          state.selectedShortPosition()?.marginRatio ?? 0.0,
        ),
      ),
    ));
  }

  /// 计算更新[买入]交易订单的详细信息
  void _updateBuyTradeOrderDetail() {
    final selectPrice = _countUserInputBaseState('price');
    final allCanBuyNumber = _countUserInputBaseState('allCanBuyNumber');
    final costPerUnit = _countUserInputBaseState('costPerUnit');
    final configMax = state.fTradeConfigModel?.maxTradeQuantity ?? 0;
    final configMin = state.fTradeConfigModel?.minTradeQuantity ?? 0;
    //
    double tempNumber = 0.0;
    OrderFraction? orderFraction;
    switch (state.userInputController.inputNumberEnum) {
      case FTradeBuySellUserInputNumberEnum.init:
        orderFraction = null;
        // 网络延迟导致服务器数据不足 会先计算出0 然后计算出正确值 导致闪烁
        if (state.accountUsableCash != null && // 余额接口正常
            state.fTradeConfigModel != null && // 证券配置正常
            (state.buyCalculateConfigList.isNotEmpty || state.sellCalculateConfigList.isNotEmpty) /** 买卖手续配置正常 */) {
          tempNumber = allCanBuyNumber;
        } else {
          tempNumber = state.userInputController.number;
        }
        break;
      case FTradeBuySellUserInputNumberEnum.selcted:
        orderFraction = state.userInputController.orderFraction;
        // A 可购买数量 大于后台最大限制
        if (allCanBuyNumber >= configMax) {
          tempNumber = (state.userInputController.orderFraction?.fraction ?? 1) * configMax;
        }
        // B 可购买数量 小于后台最大限制
        if (allCanBuyNumber < configMax) {
          tempNumber = _countUserInputBaseState('buyFractionNumber');
        }
        break;
      case FTradeBuySellUserInputNumberEnum.input:
        orderFraction = null;
        tempNumber = state.userInputController.number;
        break;
    }
    final (:number, :canDe, :canIn) =
        countUserInputNumber(number: tempNumber, maxNumber: configMax, minNumber: configMin);
    //
    final double fee = _calculateFee(
      buyOrSell: state.userInputController.tradeDirection == TradeDirection.buy,
      tradeAmount: costPerUnit * number,
      tradeNum: number,
    );
    //
    emit(state.copyWith(
      userInputController: state.userInputController.copyWith(
        price: selectPrice,
        number: number,
        canNumberDecrement: canDe,
        canNumberIncrement: canIn,
        orderFraction: orderFraction,
      ),
      longActionsController: state.longActionsController.copyWith(
        confirmTitle: 'openLong'.tr(),
        confirmEnabled: true,
        isLoadingConfirmBtn: state.fTradeConfigDataStatus == DataStatus.loading,
        needShowOptions: false,
        displayRowInfoList: FtradeLongShortActionsController.makeDisplayRowInfoList('buy', allCanBuyNumber, fee,
            costPerUnit * number, costPerUnit * number + fee, state.fTradeConfigModel?.marginRatio ?? 0.0),
      ),
      shortActionsController: state.shortActionsController.copyWith(
        confirmTitle: 'openShort'.tr(),
        confirmEnabled: true,
        isLoadingConfirmBtn: state.fTradeConfigDataStatus == DataStatus.loading,
        needShowOptions: false,
        displayRowInfoList: FtradeLongShortActionsController.makeDisplayRowInfoList('buy', allCanBuyNumber, fee,
            costPerUnit * number, costPerUnit * number + fee, state.fTradeConfigModel?.marginRatio ?? 0.0),
      ),
    ));
  }

  double _countUserInputBaseState(String countTag) {
    final double selectedPrice;
    if (state.userInputController.priceType == PriceType.market) {
      selectedPrice = state.fTradeLatestPrice ?? 0.0;
    } else {
      selectedPrice = state.userInputController.price;
    }
    if (countTag == 'price') {
      return selectedPrice;
    }
    if (state.accountUsableCash == null || state.accountUsableCash! <= 0.0 || state.fTradeConfigModel == null) {
      return 0.0;
    }
    final double multiple = max(state.fTradeConfigModel!.multiple.toDouble(), 1); // 杠杆倍率 例如:30倍
    final double marginRadio = state.fTradeConfigModel!.marginRatio * 0.01; // 保证金比例 例如:0.5表示0.5%
    final double costPerUnit = selectedPrice * marginRadio * multiple; // 杠杆缩减过后的价格
    final bool buyOrSell = state.userInputController.tradeDirection == TradeDirection.buy;
    // count 'costPerUnit'
    if (countTag == 'costPerUnit') {
      return costPerUnit;
    }
    // count 'buyFractionNumber'
    if (countTag == 'buyFractionNumber') {
      final OrderFraction selectedOrderFraction;
      if (state.userInputController.orderFraction == null) {
        selectedOrderFraction = OrderFraction.full;
      } else {
        selectedOrderFraction = state.userInputController.orderFraction!;
      }
      final double selectedUsableCash = state.accountUsableCash! * selectedOrderFraction.fraction;
      return _calculateTradeAmount(selectedUsableCash, costPerUnit, buyOrSell);
    }
    // count 'allCanBuyNumber'
    if (countTag == 'allCanBuyNumber') {
      return _calculateTradeAmount(state.accountUsableCash!, costPerUnit, buyOrSell);
    }
    assert(false, 'Error countTag');
    return 0.0;
  }

  /// 计算可买数量
  ///
  /// availableCash=> 可支配资金
  /// costPerUnit=> 杠杆后每单位价格
  double _calculateTradeAmount(double availableCash, double costPerUnit, bool buyOrSell) {
    if (costPerUnit == 0.0) {
      return 0.0;
    }
    final double estimatedNumber = availableCash / costPerUnit;
    final double estimatedFee = _calculateFee(
      buyOrSell: buyOrSell,
      tradeAmount: estimatedNumber.truncateToDouble() * costPerUnit,
      tradeNum: estimatedNumber.truncateToDouble(),
    );
    final double finalAmount = availableCash - estimatedFee;
    return (finalAmount / costPerUnit).truncateToDouble();
  }

  /// 计算手续费
  ///
  /// tradeAmount=> 交易的金额
  ///
  /// tradeNum=> 交易的数量
  ///
  /// chargeList=> 配置的交易函数
  double _calculateFee({required bool buyOrSell, required double tradeAmount, required double tradeNum}) {
    if (buyOrSell && state.sellCalculateConfigList.isEmpty) {
      return 0.0;
    }
    if (buyOrSell == false && state.sellCalculateConfigList.isEmpty) {
      return 0.0;
    }
    final feeRecord = FeeCalculator.calculateTradeHandlingFee(
      tradeAmount: tradeAmount,
      tradeNum: tradeNum,
      chargeList: buyOrSell ? state.buyCalculateConfigList : state.sellCalculateConfigList,
    );
    return feeRecord;
  }

  /*
  ============================================================================================================================
  Network
  ============================================================================================================================
  */

  void fetchUsableCash() {
    FTradeService.fetchUsableCash().then((result) {
      emit(state.copyWith(accountUsableCash: result));
      _updateTradeOrderDetail();
    });
  }

  Future<void> fetchConfigData(FTradeListItemModel fTradeListItemModel) async {
    final instrument = fTradeListItemModel.makeInstrument();
    FTradeService.fetchFTradeConfig(instrument: instrument).then((result) {
      if (result != null) {
        emit(state.copyWith(
          fTradeConfigModel: result,
          fTradeConfigDataStatus: DataStatus.success,
        ));
      } else {
        emit(state.copyWith(
          fTradeConfigDataStatus: DataStatus.failed,
        ));
        if (kDebugMode) {
          Helper.showFlutterToast(
            'Failed to fetch network',
          );
        }
      }
      _updateTradeOrderDetail();
    });
    FTradeService.fetchTradeState(fTradeListItemModel.market, fTradeListItemModel.productCode).then((result) {
      if (result != null) {
        emit(state.copyWith(
          fTradeStateModel: result,
        ));
      }
    });
    FTradeService.fetchServeFeeCalculateConfig(instrument: instrument).then((result) {
      if (result.$1.isNotEmpty || result.$2.isNotEmpty) {
        emit(state.copyWith(
          buyCalculateConfigList: result.$1,
          sellCalculateConfigList: result.$2,
          fTradeConfigDataStatus: DataStatus.success,
        ));
      } else {
        emit(state.copyWith(fTradeConfigDataStatus: DataStatus.failed));
      }
      _updateTradeOrderDetail();
    });
  }

  /*
  ============================================================================================================================
  create order
  ============================================================================================================================
  */

  void createOrder(Map<String, dynamic> queryParameters) {
    emit(state.copyWith(
      longActionsController: state.longActionsController.copyWith(isLoadingConfirmBtn: true),
      shortActionsController: state.shortActionsController.copyWith(isLoadingConfirmBtn: true),
    ));
    GPEasyLoading.showLoading();
    FTradeBuySellService.fetchCreateFTradeOrderResult(queryParameters: queryParameters).then((result) {
      GPEasyLoading.dismiss();
      if (result.$1 != true) {
        GPEasyLoading.showToast(result.$2 ?? '');
        return;
      }
      GPEasyLoading.showSuccess(message: 'orderTradeSuccess'.tr());
      emit(state.copyWith(
        longActionsController: state.longActionsController.copyWith(isLoadingConfirmBtn: false),
        shortActionsController: state.shortActionsController.copyWith(isLoadingConfirmBtn: false),
      ));
    }).catchError((e) {
      emit(state.copyWith(
        longActionsController: state.longActionsController.copyWith(isLoadingConfirmBtn: false),
        shortActionsController: state.shortActionsController.copyWith(isLoadingConfirmBtn: false),
      ));
      if (kDebugMode) {
        Helper.showFlutterToast(
          'Failed to fetch network',
        );
      }
    });
  }

  Map<String, dynamic> makeQueryParameters(String buyOrSell) {
    final number = state.userInputController.number;
    FTradeAcctOrderRecords? curPosition = state.selectedHandlePosition();
    return {
      "currency": "CNY",
      "direction": state.userInputController.tradeDirection.value,
      "expireType": 2, // 撤销前有效
      "market": itemModel.market,
      "priceType": state.userInputController.priceType.value,
      "securityType": itemModel.securityType,
      "symbol": itemModel.symbol,
      "symbolName": itemModel.name,
      "tradeNum": number.toString(),
      "tradePrice": state.userInputController.priceType == PriceType.market
          ? state.fTradeLatestPrice.toString()
          : state.userInputController.price.toString(),
      "tradeType": buyOrSell == 'buy' ? 1 : 2,
      "productCode": state.productCode,
      // 持仓ID 卖出时使用
      "positionId": curPosition?.id,
    };
  }

  List<({String label, String value, Color? valueColor, String? currency, bool showTotalToolTip})> makeConfirmList(
      bool longOrShort) {
    final costPerUnit = _countUserInputBaseState('costPerUnit');
    final number = state.userInputController.number;
    final double fee = _calculateFee(
      buyOrSell: state.userInputController.tradeDirection == TradeDirection.buy,
      tradeAmount: costPerUnit * number,
      tradeNum: number,
    );

    final longText = state.userInputController.tradeDirection == TradeDirection.buy ? 'openLong'.tr() : 'sellLong'.tr();
    final shortText =
        state.userInputController.tradeDirection == TradeDirection.buy ? 'openShort'.tr() : 'sellShort'.tr();

    return [
      (
        label: "transactionDirection".tr(),
        valueColor: longOrShort ? Colors.red : Colors.green,
        value: longOrShort ? longText : shortText,
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "accountType".tr(),
        valueColor: null,
        value: "title_futures".tr(),
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "orderType".tr(),
        valueColor: null,
        value: state.userInputController.priceType == PriceType.market ? "marketOrder".tr() : "limitOrder".tr(),
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "leverage_f".tr(),
        valueColor: null,
        value: state.fTradeConfigModel?.multiple.toString() ?? '--',
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "price".tr(),
        valueColor: null,
        value: state.userInputController.priceType == PriceType.market
            ? state.fTradeLatestPrice.toString()
            : state.userInputController.price.toString(),
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "quantity".tr(),
        valueColor: null,
        value: number.toString(),
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "margin_ratio".tr(),
        valueColor: null,
        value: '${state.fTradeConfigModel?.marginRatio ?? 0}%',
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "transactionFee".tr(),
        valueColor: null,
        value: fee.toString(),
        currency: 'CNY',
        showTotalToolTip: false,
      ),
      (
        label: "required_margin".tr(),
        valueColor: null,
        value: (costPerUnit * number).toString(),
        currency: 'CNY',
        showTotalToolTip: false,
      ),
      (
        label: "totalPrice".tr(),
        valueColor: null,
        value: (costPerUnit * number + fee).toString(),
        currency: 'CNY',
        showTotalToolTip: true,
      ),
    ];
  }
}

mixin UserInputNumberHelper {
  ({double number, bool canDe, bool canIn}) countUserInputNumber({
    required double number,
    required double maxNumber,
    required double minNumber,
  }) {
    if (minNumber <= 0 || maxNumber <= 0) {
      return (number: 0, canDe: false, canIn: false);
    }

    bool canNumberDecrement = true;
    bool canNumberIncrement = true;

    if (maxNumber > 0 && number >= maxNumber) {
      number = maxNumber;
      canNumberDecrement = true;
      canNumberIncrement = false;
    } else if (minNumber > 0 && number <= minNumber) {
      number = minNumber;
      canNumberDecrement = false;
      canNumberIncrement = true;
    }

    if (maxNumber > 0 && minNumber > 0 && number == maxNumber && number == minNumber) {
      canNumberDecrement = false;
      canNumberIncrement = false;
    }

    return (number: number, canDe: canNumberDecrement, canIn: canNumberIncrement);
  }
}
